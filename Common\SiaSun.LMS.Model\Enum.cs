﻿using System;
using System.Collections.Generic;

using System.Text;

namespace SiaSun.LMS.Enum
{
    #region FLAG

    /// <summary>
    /// 激活或非激活标识
    /// </summary>
    public enum FLAG
    {
        /// <summary>
        /// 激活
        /// </summary>
        Enable = 1,
        /// <summary>
        /// 非激活
        /// </summary>
        Disable = 0
    }

    #endregion

    #region PLAN

    /// <summary>
    /// 计划类型分组
    /// </summary>
    public enum PLAN_TYPE_GROUP
    {
        /// <summary>
        /// 仓储类
        /// </summary>
        StoreGroup = 1,
        /// <summary>
        /// 工位类
        /// </summary>
        WorkStationGroup = 2,
        /// <summary>
        /// 生产装配类
        /// </summary>
        ProduceGroup = 3
    }

    /// <summary>
    /// 计划类别
    /// </summary>
    public enum PLAN_TYPE_CODE
    {
        PlanIn,
        PlanOut,
        PlanCheck,
        PlanCheckManual,
        PlanPick,
        PlanOutEmerg,
        PlanKitOut,
        PlanArrangeOut,
        PlanOutEmptyBox
    }

    /// <summary>
    /// 计划状态
    /// </summary>
    public enum PLAN_STATUS
    {
        /// <summary>
        /// 等待执行
        /// </summary>
        Waiting,
        /// <summary>
        /// 执行中
        /// </summary>
        Executing,
        /// <summary>
        /// 暂停
        /// </summary>
        Pause,
        /// <summary>
        /// 删除
        /// </summary>
        Delete,
        /// <summary>
        /// 终止
        /// </summary>
        Stop,
        /// <summary>
        /// 等待排产
        /// </summary>
        RouteWaiting,
        /// <summary>
        /// 等待生产
        /// </summary>
        ProduceWaiting,
        /// <summary>
        /// 执行生产
        /// </summary>
        ProduceExecuting,
        /// <summary>
        /// 暂停生产
        /// </summary>
        ProducePause,
        /// <summary>
        /// 完成
        /// </summary>
        Finish,
        /// <summary>
        /// 审核完毕
        /// </summary>
        Complete
    }

    /// <summary>
    /// 工艺流程任务类型
    /// </summary>
    public enum PLAN_INOUT
    {
        /// <summary>
        /// 入库
        /// </summary>
        In = 1,
        /// <summary>
        /// 出库
        /// </summary>
        Out = 2,
        /// <summary>
        /// 移库
        /// </summary>
        Move = 3,
        /// <summary>
        /// 拣选，回流
        /// </summary>
        Sort = 4
    }

    #endregion

    #region MANAGE-CONTROL

    /// <summary>
    /// 计划/任务来源
    /// </summary>
    public enum TASK_SOURCE
    {
        WMS,
        WES
    }

    /// <summary>
    /// 管理任务类型
    /// </summary>
    public enum MANAGE_TYPE
    {
        ////入库
        //IN = 101,
        ////出库
        //OUT = 201,
        ////配盘
        //BINDING = 301,
        ////取消配盘
        //CANCLEBINDING = 302,
        ////移库
        //MOVE = 303,
        ////上架
        //UP = 401,
        ////搬运容器上架
        //UPPALLET = 402,
        ////下架
        //DOWN = 501,
        ////搬运容器下架
        //DOWNPALLET = 502,

        ManageDown,
        ManageIn,
        ManageMove,
        ManageOut,
        ManageProductOut,
        ManageUp,
        ManageStockIn,
        ManageStockOut,
        ManageTrans,
        ManageCheckDown,
        ManageCheckDownLocal,
        ManageCheckUp,
        ManageKitUp,
        ManagePick,
        ManageAdjust,
        ManageArrangeUp,
        ManageArrangeDown,
        ManageArrangeGoodsDown,
        ManageManualPick
    }

    /// <summary>
    /// 任务状态
    /// </summary>
    public enum MANAGE_STATUS
    {
        WaitingSend,
        WaitingExecute,
        //Waitting,
        Error,
        Cancel,
        Complete,
        ExceptionComplete,
        Executing = 10,
        WaitingConfirm = 20,
        ConfirmFinish = 30,
    }

    /// <summary>
    /// 控制任务状态
    /// </summary>
    public enum CONTROL_STATUS
    {
        /// <summary>
        /// 等待
        /// </summary>
        Wait = 0,
        /// <summary>
        /// 调度已经获取任务
        /// </summary>
        Control_Readed = 7,
        /// <summary>
        /// 开始运行
        /// </summary>
        Runing = 10,
        /// <summary>
        /// 堆垛机运行
        /// </summary>
        DeviceRuning = 11,
        /// <summary>
        /// 调度申请改道
        /// </summary>
        LterRouteApply = 30,
        /// <summary>
        /// 管理答复改道申请
        /// </summary>
        LterRouteReplay = 40,
        /// <summary>
        /// 异常完成
        /// </summary>
        TaskAbend = 990,
        /// <summary>
        /// 任务被删除
        /// </summary>
        TaskDelete = 900,
        /// <summary>
        /// 堆垛机的取空处理
        /// </summary>
        EmptyOutPut = 980,
        /// <summary>
        /// 堆垛机的送货重需要改路径处理
        /// </summary>
        RepeatInput = 970,
        /// <summary>
        /// 任务完成
        /// </summary>
        Finish = 999,
        /// <summary>
        /// WebService调用临时任务
        /// </summary>
        TempStatus = -7
    }

    /// <summary>
    /// 指令申请状态
    /// </summary>
    public enum CONTROL_APPLY_STATUS
    {
        /// <summary>
        /// 等待
        /// </summary>
        Waiting = 0,
        /// <summary>
        /// 读取
        /// </summary>
        Read = 1,
        /// <summary>
        /// 完成
        /// </summary>
        Finish = 2,
        /// <summary>
        /// 异常
        /// </summary>
        Error = 3
    }

    /// <summary>
    /// 
    /// </summary>
    public enum CONTROL_TYPE
    {
        Up = 1,
        Down = 2,
        Move = 3,
        MoveStation = 4
    }

    /// <summary>
    /// 整理工位的工作模式
    /// </summary>
    public enum ArrangeWorkMode
    {
        //合箱模式
        Arrange,
        //整理未满箱
        ArrangeGoods,
        //紧急出库
        Emerg
    }

    /// <summary>
    /// 整理工位的工作模式
    /// </summary>
    public enum WorkModeKitOut
    {
        //手动模式
        Manual,
        //自动模式
        Auto
    }

    /// <summary>
    /// 齐套出库单据模式
    /// </summary>
    public enum PlanModeKitOut
    {
        //混单模式
        Mix,
        //单一模式
        One
    }

    #endregion

    #region WAREHOUSE

    /// <summary>
    /// 库区类型
    /// </summary>
    public enum AREA_TYPE
    {
        /// <summary>
        /// 立库
        /// </summary>
        LiKu,
        /// <summary>
        /// 虚拟库/平库
        /// </summary>
        XuNiKu
    }

    /// <summary>
    /// 货位类型
    /// </summary>
    public enum CELL_TYPE
    {
        /// <summary>
        /// 货位
        /// </summary>
        Cell,
        /// <summary>
        /// 站台/输送台
        /// </summary>
        Station,
        /// <summary>
        /// 异常站台/工位
        /// </summary>
        ErrorStation,
        /// <summary>
        /// 生产工位
        /// </summary>
        WorkStation
    }

    /// <summary>
    /// 货位存储类型
    /// </summary>
    public enum CELL_STORAGE_TYPE
    {
        /// <summary>
        /// 存储单托盘
        /// </summary>
        Single,
        /// <summary>
        /// 存储多托盘
        /// </summary>
        Multiple

    }

    /// <summary>
    /// 货位类别
    /// </summary>
    public enum CELL_FORK_TYPE
    {
        /// <summary>
        /// 普通
        /// </summary>
        Normal,
        /// <summary>
        /// 双伸
        /// </summary>
        Double,
        /// <summary>
        /// 双叉
        /// </summary>
        Multi
    }

    /// <summary>
    /// 货位任务类型
    /// </summary>
    public enum CELL_INOUT
    {
        /// <summary>
        /// 入
        /// </summary>
        In = 1,
        /// <summary>
        /// 出
        /// </summary>
        Out = 2,
        /// <summary>
        /// 可入可出
        /// </summary>
        InOut = 3
    }

    /// <summary>
    /// 货位存储状态
    /// </summary>
    public enum CELL_STATUS
    {
        /// <summary>
        /// 满货
        /// </summary>
        Full,
        /// <summary>
        /// 有货
        /// </summary>
        Have,
        /// <summary>
        /// 无货
        /// </summary>
        Nohave,
        /// <summary>
        /// 空托盘
        /// </summary>
        Pallet,
        /// <summary>
        /// 异常货位
        /// </summary>
        Exception,

        /// <summary>
        /// 禁用
        /// </summary>
        Forbiden
    }
    

    /// <summary>
    /// 货位运行状态
    /// </summary>
    public enum RUN_STATUS
    {
        /// <summary>
        /// 禁用
        /// </summary>
        Disable,
        /// <summary>
        /// 待用
        /// </summary>
        Enable,
        /// <summary>
        /// 运行
        /// </summary>
        Run,
        /// <summary>
        /// 选定
        /// </summary>
        Selected
    }

    /// <summary>
    /// 任务执行的设备类型
    /// </summary>
    public enum DEVICE_TYPE
    {
        /// <summary>
        /// 系统设备
        /// </summary>
        system,
        /// <summary>
        /// 自动设备
        /// </summary>
        auto,
        /// <summary>
        /// AGV设备
        /// </summary>
        agv
    }


    /// <summary>
    /// 日志级别
    /// </summary>
    public enum LOG_LEVEL
    {
        /// <summary>
        /// 紧急
        /// </summary>
        //Emergency,
        /// <summary>
        /// 警告
        /// </summary>
        //Alert,
        /// <summary>
        /// 关键
        /// </summary>
        Critical,
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        ///// <summary>
        ///// 告警
        ///// </summary>
        Warn,
        /// <summary>
        /// 通知
        /// </summary>
        //Notification,
        /// <summary>
        /// 信息
        /// </summary>
        Information,
        /// <summary>
        /// 调试
        /// </summary>
        Debug
    }

    public enum LogThread
    {
        Plan,
        Task,
        Control,
        Apply,
        Interface,
        Scaner,
        Storage,
        System
    }

    public enum CellModel
    {
        /// <summary>
        /// 空箱
        /// </summary>
        EmptyBox = 0,
        /// <summary>
        /// 一格箱
        /// </summary>
        GoodsBox1=1,
        /// <summary>
        /// 二格箱
        /// </summary>
        GoodsBox2 = 2,
        /// <summary>
        /// 三格箱
        /// </summary>
        GoodsBox3 = 3,
        /// <summary>
        /// 四格箱
        /// </summary>
        GoodsBox4 = 4,
        /// <summary>
        /// 六格箱
        /// </summary>
        GoodsBox6 = 6,
        /// <summary>
        /// 齐套箱
        /// </summary>
        KitBox = 100,
        /// <summary>
        /// 待拣选箱
        /// </summary>
        PickingBox=99
    }

    #endregion

    #region PRODUCE

    /// <summary>
    /// 物料状态
    /// </summary>
    public enum GOODS_STATUS
    {
        /// <summary>
        /// 待加工
        /// </summary>
        Waiting = 1,
        /// <summary>
        /// 执行中
        /// </summary>
        Executing = 2,
        /// <summary>
        /// 工序完工
        /// </summary>
        Finish = 3,
        /// <summary>
        /// 制件完工
        /// </summary>
        Complet = 4
    }

    /// <summary>
    /// 工作模式：串行模式或并行模式
    /// </summary>
    public enum WORK_MODE
    {
        /// <summary>
        /// 串行
        /// </summary>
        Serial = 1,
        /// <summary>
        /// 并行
        /// </summary>
        Parallel = 2
    }

    /// <summary>
    /// 排产状态
    /// </summary>
    public enum ROUTE_STATUS
    {
        /// <summary>
        /// 等待排产
        /// </summary>
        Waiting,
        /// <summary>
        /// 排产执行
        /// </summary>
        Executing,
        /// <summary>
        /// 排产暂停
        /// </summary>
        Pause,
        /// <summary>
        /// 排产停止
        /// </summary>
        Stop,
        /// <summary>
        /// 排产完成
        /// </summary>
        Finish
    }

    /// <summary>
    /// 工位操作模式
    /// </summary>
    public enum STATION_OP_MODE
    {
        /// <summary>
        /// 送料模式
        /// </summary>
        Send = 1,

        /// <summary>
        /// 生产模式
        /// </summary>
        Produce = 2,

        /// <summary>
        /// 回库模式
        /// </summary>
        Back = 3
    }

    #endregion

    #region ERP

    /// <summary>
    /// ERP导入任务状态
    /// </summary>
    public enum ERP_TASK_FLAG
    {
        /// <summary>
        /// 未处理
        /// </summary>
        Waiting=0,

        /// <summary>
        /// 任务完成
        /// </summary>
        Finish=1,

        /// <summary>
        /// 已回填
        /// </summary>
        ERPHandle=2,

        /// <summary>
        /// 导入成功
        /// </summary>
        WHImportSuccess=3,

        /// <summary>
        /// 导入错误
        /// </summary>
        WHImportError=4
    }

    #endregion
}
