using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
using System.Data;

namespace SiaSun.LMS.WinService
{
    /// <summary>
    /// 双深货位延迟任务处理定时作业
    /// 处理状态为"PendingControlCreate"的管理任务，检查外侧货位冲突并创建控制任务
    /// </summary>
    [DisallowConcurrentExecution]
    public class PendingControlCreateJob : IJob
    {
        /// <summary>
        /// 定时任务执行入口
        /// </summary>
        /// <param name="context">任务执行上下文</param>
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                // 调用业务服务处理延迟任务
                string sResult = string.Empty;
                bool bResult = MainApp.BaseService._S_ManageService.ProcessPendingControlCreateTasks(out sResult);

                if (!bResult && !string.IsNullOrEmpty(sResult))
                {
                    Program.sysLog.WarnFormat("PendingControlCreateJob.Execute:处理延迟任务失败_{0}", sResult);
                }
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("PendingControlCreateJob.Execute:异常", ex);
                // 不重新抛出异常，避免影响调度器
            }
        }
    }
}
