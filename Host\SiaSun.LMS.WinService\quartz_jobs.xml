﻿<job-scheduling-data xmlns="http://quartznet.sourceforge.net/JobSchedulingData" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2.0">
  <processing-directives>
       <overwrite-existing-data>true</overwrite-existing-data>   
  </processing-directives>

  <!--job节点 配置说明
     name(必填) 任务名称，同一个group中多个job的name不能相同，若未设置group则所有未设置group的job为同一个分组，如:<name>sampleJob</name>
     group(选填) 任务所属分组，用于标识任务所属分组，如:<group>sampleGroup</group>
     description(选填) 任务描述，用于描述任务具体内容，如:<description>Sample job for Quartz Server</description>
     job-type(必填) 任务类型，任务的具体类型及所属程序集，格式：实现了IJob接口的包含完整命名空间的类名,程序集名称，如:<job-type>Quartz.Server.SampleJob, Quartz.Server</job-type>
     durable(选填) 具体作用不知，官方示例中默认为true，如:<durable>true</durable>
     recover(选填) 具体作用不知，官方示例中默认为false，如:<recover>false</recover>-->

      <!--cron节点配置说明
      复杂任务触发器使用cron表达式定制任务调度（强烈推荐)
      name(必填) 触发器名称，同一个分组中的名称必须不同
      group(选填) 触发器组
      description(选填) 触发器描述
      job-name(必填) 要调度的任务名称，该job-name必须和对应job节点中的name完全相同
      job-group(选填) 调度任务(job)所属分组，该值必须和job中的group完全相同
      start-time(选填) 任务开始执行时间utc时间，北京时间需要+08:00，如：<start-time>2012-04-01T08:00:00+08:00</start-time>表示北京时间2012年4月1日上午8:00开始执行，注意服务启动或重启时都会检测此属性，若没有设置此属性，服务会根据cron-expression的设置执行任务调度；若start-time设置的时间比当前时间较早，则服务启动后会忽略掉cron-expression设置，立即执行一次调度，之后再根据cron-expression执行任务调度；若设置的时间比当前时间晚，则服务会在到达设置时间相同后才会应用cron-expression，根据规则执行任务调度，一般若无特殊需要请不要设置此属性
      cron-expression(必填) cron表达式，如:<cron-expression>0/10 * * * * ?</cron-expression>每10秒执行一次-->

  <!--Cron表达式使用说明
         
         Cron表达式被用来配置CronTrigger实例。Cron表达式是一个由7个子表达式组成的字符串。每个子表达式都描述了一个单独的日程细节。这些子表达式用空格分隔，分别表示：

         1. Seconds 秒

         2. Minutes 分钟

         3. Hours 小时

         4. Day-of-Month 月中的天

         5. Month 月

         6. Day-of-Week 周中的天

         7. Year (optional field) 年（可选的域）

         一个cron表达式的例子字符串为"0 0 12 ? * WED",这表示“每周三的中午12：00”。

         单个子表达式可以包含范围或者列表。例如：前面例子中的周中的天这个域（这里是"WED"）可以被替换为"MON-FRI", "MON, WED, FRI"或者甚至"MON-WED,SAT"。

         通配符（'*'）可以被用来表示域中“每个”可能的值。因此在"Month"域中的*表示每个月，而在Day-Of-Week域中的*则表示“周中的每一天”。

         所有的域中的值都有特定的合法范围，这些值的合法范围相当明显，例如：秒和分域的合法值为0到59，小时的合法范围是0到23，Day-of-Month中值得合法凡范围是0到31，但是需要注意不同的月份中的天数不同。月份的合法值是0到11。或者用字符串JAN,FEB MAR, APR, MAY, JUN, JUL, AUG, SEP, OCT, NOV 及DEC来表示。Days-of-Week可以用1到7来表示（1=星期日）或者用字符串SUN, MON, TUE, WED, THU, FRI 和SAT来表示.

         '/'字符用来表示值的增量，例如, 如果分钟域中放入'0/15'，它表示“每隔15分钟，从0开始”，如果在份中域中使用'3/20'，则表示“小时中每隔20分钟，从第3分钟开始”或者另外相同的形式就是'3,23,43'。

         '?'字符可以用在day-of-month及day-of-week域中，它用来表示“没有指定值”。这对于需要指定一个或者两个域的值而不需要对其他域进行设置来说相当有用。

         'L'字符可以在day-of-month及day-of-week中使用，这个字符是"last"的简写，但是在两个域中的意义不同。例如，在day-of-month域中的"L"表示这个月的最后一天，即，一月的31日，非闰年的二月的28日。如果它用在day-of-week中，则表示"7"或者"SAT"。但是如果在day-of-week域中，这个字符跟在别的值后面，则表示"当月的最后的周XXX"。例如："6L" 或者 "FRIL"都表示本月的最后一个周五。当使用'L'选项时，最重要的是不要指定列表或者值范围，否则会导致混乱。

         'W' 字符用来指定距离给定日最接近的周几（在day-of-week域中指定）。例如：如果你为day-of-month域指定为"15W",则表示“距离月中15号最近的周几”。

         '#'表示表示月中的第几个周几。例如：day-of-week域中的"6#3" 或者 "FRI#3"表示“月中第三个周五”。

         作为一个例子，下面的Quartz.NET克隆表达式将在星期一到星期五的每天上午10点15分执行一个作业。
         0 15 10 ? * MON-FRI

         下面的表达式
         0 15 10 ? * 6L 2007-2010
         将在2007年到2010年的每个月的最后一个星期五上午10点15分执行作业。-->

  <schedule>
    <!--<job>
       <name>taskCompleteJob</name>
       <group>taskCompleteGroup</group>
       <description>deal with task complete</description>
       <job-type>SiaSun.LMS.WinService.TaskCompleteJob, WinService</job-type>
       <durable>true</durable>
       <recover>true</recover>
     </job>
    <trigger>
      <cron>
        <name>taskCompleteTrigger</name>
        <group>Group1</group>
        <description>deal with task complete</description>
        <job-name>taskCompleteJob</job-name>
        <job-group>taskCompleteGroup</job-group>
        <cron-expression>0/1 * * * * ?</cron-expression>
      </cron>
    </trigger>    
    
     <job>
       <name>taskApplyJob</name>
       <group>taskApplyGroup</group>
       <description>deal with task apply</description>
       <job-type>SiaSun.LMS.WinService.TaskApplyJob, WinService</job-type>
       <durable>true</durable>
       <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>taskApplyTrigger</name>
        <group>Group2</group>
        <description>deal with task apply</description>
        <job-name>taskApplyJob</job-name>
        <job-group>taskApplyGroup</job-group>
        <cron-expression>0/1 * * * * ?</cron-expression>
      </cron>
    </trigger>

    <job>
       <name>ClearReturnTaskJob</name>
       <group>ClearReturnTaskGroup</group>
       <description>deal with return task</description>
       <job-type>SiaSun.LMS.WinService.ClearReturnTaskJob, WinService</job-type>
       <durable>true</durable>
       <recover>false</recover>
     </job>
    <trigger>
      <cron>
        <name>ClearReturnTaskTrigger</name>
        <group>Group3</group>
        <description>deal with return task</description>
        <job-name>ClearReturnTaskJob</job-name>
        <job-group>ClearReturnTaskGroup</job-group>
        <cron-expression>0/1 * * * * ?</cron-expression>
      </cron>
    </trigger>-->

    <job>
      <name>ControlJob</name>
      <group>ControlJobGroup</group>
      <description>deal with task complete</description>
      <job-type>SiaSun.LMS.WinService.ControlJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>ControlJobTrigger</name>
        <group>Group1</group>
        <description>deal with task complete</description>
        <job-name>ControlJob</job-name>
        <job-group>ControlJobGroup</job-group>
        <cron-expression>0/1 * * * * ?</cron-expression>
      </cron>
    </trigger>

    <job>
      <name>ApplyJob</name>
      <group>ApplyJobGroup</group>
      <description>deal with task apply</description>
      <job-type>SiaSun.LMS.WinService.ApplyJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>ApplyJobTrigger</name>
        <group>Group2</group>
        <description>deal with task apply</description>
        <job-name>ApplyJob</job-name>
        <job-group>ApplyJobGroup</job-group>
        <cron-expression>0/1 * * * * ?</cron-expression>
      </cron>
    </trigger>

    <job>
      <name>AutoOutEmptyBoxJob</name>
      <group>AutoOutEmptyBoxGroup</group>
      <description>auto out emptybox for pickstation</description>
      <job-type>SiaSun.LMS.WinService.AutoOutEmptyBoxJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>AutoOutEmptyBoxTrigger</name>
        <group>Group4</group>
        <description>auto out emptybox for pickstation</description>
        <job-name>AutoOutEmptyBoxJob</job-name>
        <job-group>AutoOutEmptyBoxGroup</job-group>
        <cron-expression>30 0/1 * * * ?</cron-expression>
      </cron>
    </trigger>

    <job>
      <name>SystemCleanJob</name>
      <group>SystemCleanGroup</group>
      <description>clear sysLog plan record</description>
      <job-type>SiaSun.LMS.WinService.SystemCleanJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>SystemCleanTrigger</name>
        <group>Group5</group>
        <description>clear sysLog plan record</description>
        <job-name>SystemCleanJob</job-name>
        <job-group>SystemCleanGroup</job-group>
        <cron-expression>0 0 23 * * ?</cron-expression>
      </cron>
    </trigger>

    <job>
      <name>CaluGoodsUsagerateJob</name>
      <group>CaluGoodsUsagerateGroup</group>
      <description></description>
      <job-type>SiaSun.LMS.WinService.CaluGoodsUsagerateJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>CaluGoodsUsagerateTrigger</name>
        <group>Group6</group>
        <description></description>
        <job-name>CaluGoodsUsagerateJob</job-name>
        <job-group>CaluGoodsUsagerateGroup</job-group>
        <cron-expression>0 0 2 * * ?</cron-expression>
        <!--<cron-expression>0/20 * * * * ?</cron-expression>-->
      </cron>
    </trigger>

    <job>
      <name>CaluCellOutScoreJob</name>
      <group>CaluCellOutScoreGroup</group>
      <description></description>
      <job-type>SiaSun.LMS.WinService.CaluCellOutScoreJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>CaluCellOutScoreTrigger</name>
        <group>Group7</group>
        <description></description>
        <job-name>CaluCellOutScoreJob</job-name>
        <job-group>CaluCellOutScoreGroup</job-group>
        <cron-expression>0 0 3 * * ?</cron-expression>
        <!--<cron-expression>0/20 * * * * ?</cron-expression>-->
      </cron>
    </trigger>


    <!--<job>
      <name>AutoMoveKitboxJob</name>
      <group>AutoMoveKitboxGroup</group>
      <description></description>
      <job-type>SiaSun.LMS.WinService.AutoMoveKitboxJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>AutoMoveKitboxTrigger</name>
        <group>Group8</group>
        <description></description>
        <job-name>AutoMoveKitboxJob</job-name>
        <job-group>AutoMoveKitboxGroup</job-group>
        --><!--<cron-expression>0 */1 9-16 * * ?</cron-expression>--><!--
        <cron-expression>0 */1 * * * ?</cron-expression>
        --><!--<cron-expression>0/20 * * * * ?</cron-expression>--><!--
      </cron>
    </trigger>-->

    <!--<job>
      <name>PickStationBindJob</name>
      <group>PickStationBindJobGroup</group>
      <description>deal with auto bind plan_group to pick_station</description>
      <job-type>SiaSun.LMS.WinService.PickStationBindJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>PickStationBindTrigger</name>
        <group>Group10</group>
        <description></description>
        <job-name>PickStationBindJob</job-name>
        <job-group>PickStationBindJobGroup</job-group>
        <cron-expression>0/30 * * * * ?</cron-expression>
      </cron>
    </trigger>-->

    <!-- 双深货位延迟任务处理定时作业 -->
    <job>
      <name>PendingControlCreateJob</name>
      <group>PendingControlCreateGroup</group>
      <description>处理双深货位延迟创建的控制任务</description>
      <job-type>SiaSun.LMS.WinService.PendingControlCreateJob, WinService</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>PendingControlCreateTrigger</name>
        <group>Group11</group>
        <description>每2分钟检查一次延迟任务</description>
        <job-name>PendingControlCreateJob</job-name>
        <job-group>PendingControlCreateGroup</job-group>
        <cron-expression>0 0/2 * * * ?</cron-expression>
        <!-- 每2分钟执行一次，避免对数据库造成过大压力 -->
      </cron>
    </trigger>

  </schedule>

</job-scheduling-data>
