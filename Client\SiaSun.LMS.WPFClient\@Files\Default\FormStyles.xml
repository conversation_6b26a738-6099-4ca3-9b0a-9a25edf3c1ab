﻿<?xml version="1.0" encoding="utf-8"?>
<Styles>
  <!--<Form Name="PLAN_ACTION">
    <Table Name="V_PLAN_LIST">
      <Field Column="PLAN_LIST_ID" DbType="String" Header="计划列表ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="9" AllowQuery="1" QueryOperation="like" />
      <Field Column="PLAN_LIST_CODE" DbType="String" Header="分录号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_UNITS" DbType="string" Header="基本单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="分配数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="31" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="32" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_UNFINISHED_QUANTITY" DbType="decimal" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="33" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="40" AllowQuery="71" QueryOperation="" />
    </Table>
  </Form>  
  
  <Form Name="PLAN_IMPORT">
    <Table Name="U5WMCSINTERFACE">
      <Field Column="WMI_TASKNO" DbType="String" Header="单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="like" />
      <Field Column="WMI_TASKTYPE" DbType="String" Header="入出库类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="like" />
      <Field Column="WMI_STATUS" DbType="string" Header="状态" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
      <Field Column="WMI_CREATEDATE" DbType="string" Header="时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
      <Field Column="WMI_PART" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
      <Field Column="WMI_QTY" DbType="String" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="31" AllowQuery="0" QueryOperation="" />
      <Field Column="WMI_COMMENT" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="32" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>
  
  <Form Name="MANAGE_IN_TEST">
    <Table Name="V_MANAGE_LIST">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="规格" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_UNITS" DbType="String" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="String" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="like" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="String" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_REMARK" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>
  
  <Form Name="MANAGE_PLAN_IN">
    <Table Name="V_PLAN_LIST">
      <Field Column="PLAN_ID" DbType="String" Header="计划ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="String" Header="组盘数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="31" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="分配数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="32" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="33" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_UNFINISHED_QUANTITY" DbType="decimal" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="34" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_REMARK" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="71" AllowQuery="41" QueryOperation="" />
    </Table>
  </Form>
  
  <Form Name="MANAGE_OUT">
    <Table Name="V_STORAGE_LIST">
      <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="9" AllowQuery="1" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="原名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="分类" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY3" DbType="String" Header="尺寸" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="14" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY4" DbType="String" Header="模具号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="15" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY5" DbType="String" Header="模具出数" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="16" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="17" AllowQuery="0" QueryOperation="" />
      <Field Column="DEVICE_CODE" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="SELECT DEVICE_CODE as value,DEVICE_CODE as name FROM WH_CELL WHERE DEVICE_CODE &gt; '18000' GROUP BY DEVICE_CODE ORDER BY DEVICE_CODE" Remark="" Order="18" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="19" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="货位规格" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="20" AllowQuery="1" QueryOperation="" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="21" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="22" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="String" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="23" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>
  
  <Form Name="MANAGE_PLAN_OUT">
    <Table Name="V_STORAGE_LIST">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="规格" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="String" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="21" AllowQuery="1" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="string" Header="出库数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="40" AllowQuery="0" QueryOperation="" />
      <Field Column="UPDATE_TIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="41" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>-->


  <Form Name="MANAGE_IN">
    <Table Name="V_MANAGE_LIST">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="ABC分类" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="SN码拣选" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="String" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="BOX_BARCODE" DbType="Decimal" Header="所在箱格" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="WDZ_BOX_GRID" Remark="" Order="155" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>

  <Form Name="MANAGE_ADJUST">
    <Table Name="V_STORAGE_LIST">
      <!--<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="规格" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="String" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="21" AllowQuery="1" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="string" Header="实际数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="40" AllowQuery="0" QueryOperation="" />
      <Field Column="UPDATE_TIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="41" AllowQuery="0" QueryOperation="" />-->

      <Field Column="STOCK_BARCODE" DbType="String" Header="容器条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="112" AllowQuery="1" QueryOperation="" />
      <Field Column="BOX_BARCODE" DbType="String" Header="所在箱格" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="WDZ_BOX_GRID" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="ABC分类" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="SN码拣选" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="160" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="like" />
      <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="AREA_ID" DbType="String" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select AREA_ID as VALUE ,AREA_NAME as NAME from WH_AREA " Remark="" Order="185" AllowQuery="1" QueryOperation="" />
      <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="1" QueryOperation="=" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="1" QueryOperation="like" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="string" Header="实际数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>

	<Form Name="MANAGE_CHECK">
		<Table Name="V_STORAGE_LIST">

		<Field Column="STOCK_BARCODE" DbType="String" Header="容器条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="" />
		<Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="1" QueryOperation="" />
		<Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="112" AllowQuery="1" QueryOperation="" />
		<Field Column="BOX_BARCODE" DbType="String" Header="所在箱格" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="WDZ_BOX_GRID" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
		<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
		<Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="1" QueryOperation="like" />
		<!--<Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="ABC分类" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="1" QueryOperation="like" />
		<Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="SN码拣选" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="160" AllowQuery="1" QueryOperation="like" />-->
		<Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="like" />
		<Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
		<Field Column="AREA_ID" DbType="String" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select AREA_ID as VALUE ,AREA_NAME as NAME from WH_AREA " Remark="" Order="185" AllowQuery="1" QueryOperation="" />
		<Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="1" QueryOperation="=" />
		<Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="1" QueryOperation="like" />
		<Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
		<Field Column="MANAGE_LIST_QUANTITY" DbType="string" Header="实际数量" ControlType="TextBox" DefaultValue="-1" ReadOnly="0" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
		<Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_PROPERTY9" DbType="" Header="上次盘点单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="300" AllowQuery="1" QueryOperation="like" />
			<Field Column="GOODS_PROPERTY10" DbType="" Header="上次盘点时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="310" AllowQuery="0" QueryOperation="like" />
			<Field Column="GOODS_PROPERTY11" DbType="" Header="上次库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="320" AllowQuery="0" QueryOperation="like" />
			<Field Column="GOODS_PROPERTY12" DbType="" Header="上次实际数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="330" AllowQuery="0" QueryOperation="like" />
			<Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="400" AllowQuery="0" QueryOperation="" />

		</Table>
	</Form>

  <Form Name="PLAN_EDIT">
    <Table Name="PLAN_MAIN">
      <Field Column="PLAN_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_RELATIVE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_ROOT_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CODE" DbType="String" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_TYPE_CODE" DbType="Int32" Header="计划类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="请选择类型|nonenull" DataBind="SELECT PLAN_TYPE_CODE AS value, PLAN_TYPE_NAME AS name FROM PLAN_TYPE WHERE PLAN_TYPE_CODE in ('PlanArrangeOut','PlanOutEmptyBox','PlanCheckManual')" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_INOUT_STATION" DbType="String" Header="出入站台" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select CELL_CODE as value , CELL_NAME as name from WH_CELL where CELL_TYPE='Station' and CELL_FLAG='1'" Remark="" Order="145" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_FROM_DEPT" DbType="Int32" Header="" ControlType="ComboBox" DefaultValue="-1" ReadOnly="0" Validation="请选择类型|nonenull" DataBind="select Convert(varchar(50),project_id) AS value,project_qkcode+project_name as name from project_main where PROJECT_FLAG='1'" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CREATE_TIME" DbType="String" Header="创建时间" ControlType="DateTimePicker" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_BEGIN_TIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_END_TIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="311" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_STATUS" DbType="String" Header="计划状态" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="PLAN_STATUS" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CREATER" DbType="String" Header="操作员" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_FROM_USER" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_TO_USER" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="900" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_FLAG" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    </Table>
    
    <Table Name="V_PLAN_LIST">
      <!--<Field Column="PLAN_LIST_ID" DbType="String" Header="计划列表ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="like" />-->
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_UNITS" DbType="string" Header="基本单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <!--<Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="分配数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="1" QueryOperation="" />-->
    </Table>
  </Form>  
  
  <Form Name="MANAGE_STORAGE_IN">
    <Table Name="V_STORAGE_LIST_LOCAL">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="分类" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY3" DbType="String" Header="尺寸" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="14" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY4" DbType="String" Header="模具号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="15" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY5" DbType="String" Header="模具出数" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="16" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY6" DbType="String" Header="高度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="TZYG_WL_HEIGHT" Remark="" Order="17" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="18" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="19" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
    </Table>
    
    <Table Name="V_STORAGE_LIST">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="分类" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY3" DbType="String" Header="尺寸" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="14" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY4" DbType="String" Header="模具号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="15" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY5" DbType="String" Header="模具出数" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="16" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY6" DbType="String" Header="高度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="TZYG_WL_HEIGHT" Remark="" Order="17" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="18" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="19" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>
  
  <Form Name="MANAGE_STOCK_OUT">
    <Table Name="V_STORAGE_LIST">
      <Field Column="STOCK_BARCODE" DbType="String" Header="容器条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="112" AllowQuery="1" QueryOperation="" />
      <Field Column="BOX_BARCODE" DbType="String" Header="所在箱格" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="WDZ_BOX_GRID" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="like" />
      <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="AREA_ID" DbType="String" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select AREA_ID as VALUE ,AREA_NAME as NAME from WH_AREA " Remark="" Order="185" AllowQuery="1" QueryOperation="" />
      <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="190" AllowQuery="1" QueryOperation="=" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>
  
  <Form Name="GOODS_TEMPLATE">
    <Table Name="V_GOODS_TEMPLATE">
      <Field Column="GOODS_TEMPLATE_CODE" DbType="String" Header="方案编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_TEMPLATE_NAME" DbType="String" Header="方案名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="string" Header="产品编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="产品名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="21" AllowQuery="1" QueryOperation="" />
      <Field Column="TEMPLATE_FLAG" DbType="String" Header="启用标记" ControlType="CheckBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_TEMPLATE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="31" AllowQuery="0" QueryOperation="" />
    </Table>
    
    <Table Name="V_GOODS_TEMPLATE_LIST">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="21" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="22" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="23" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_TEMPLATE_QUANTITY" DbType="String" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>
  
  <Form Name="DateSectSplitQueryWindow">
    <Table Name="V_PLAN_LIST">
      <Field Column="PLAN_ID" DbType="String" Header="计划ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="" />
      <Field Column="PLAN_LIST_ID" DbType="String" Header="计划列表ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_GROUP" DbType="string" Header="拣选单唯一码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="125" AllowQuery="1" QueryOperation="" />
      <Field Column="PLAN_CODE" DbType="string" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="like" />
      <Field Column="PLAN_TYPE_CODE" DbType="String" Header="计划类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="SELECT PLAN_TYPE_CODE as value,PLAN_TYPE_NAME as name FROM PLAN_TYPE" Remark="" Order="140" AllowQuery="1" QueryOperation="" />
      <Field Column="PLAN_FLAG" DbType="String" Header="源系统" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CREATER" DbType="String" Header="操作人员" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_STATUS" DbType="String" Header="状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="PLAN_STATUS" Remark="" Order="170" AllowQuery="1" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="180" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="191" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="210" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="分配数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_QUANTITY_APPEND" DbType="String" Header="盘点附加数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_PICKED_QUANTITY" DbType="String" Header="拣选完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="237" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CREATE_TIME" DbType="string" Header="制单时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="like" />
      <Field Column="PLAN_BEGIN_TIME" DbType="string" Header="开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="260" AllowQuery="0" QueryOperation="like" />
      <Field Column="PLAN_END_TIME" DbType="string" Header="完成时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="270" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_HEADTEXT" DbType="string" Header="抬头文本" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="275" AllowQuery="0" QueryOperation="" />

      <Field Column="PLAN_TO_USER" DbType="string" Header="集货号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="290" AllowQuery="1" QueryOperation="like" />
      <Field Column="PLAN_FROM_DEPT" DbType="String" Header="使用时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="300" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_TO_DEPT" DbType="string" Header="使用位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_FROM_USER" DbType="string" Header="自动到线边完成" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="320" AllowQuery="1" QueryOperation="like" />
      <Field Column="PLAN_RELATEDBILL" DbType="String" Header="派工单" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="330" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_SHOPNO" DbType="string" Header="车间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="340" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_WORKINGSEATL" DbType="string" Header="工位" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="350" AllowQuery="1" QueryOperation="like" />
	  <Field Column="BACKUP_FILED1" DbType="string" Header="MES需求时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="360" AllowQuery="1" QueryOperation="like" />
	  <Field Column="PLAN_RELATIVE_CODE" DbType="string" Header="拣选任务单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="363" AllowQuery="1" QueryOperation="like" />
	  <Field Column="PLAN_PROJECT_CODE" DbType="string" Header="项目号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="367" AllowQuery="1" QueryOperation="like" />
		
	  <Field Column="PLAN_LIST_REMARK" DbType="String" Header="备注1" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="370" AllowQuery="71" QueryOperation="" />
      <Field Column="PLAN_REMARK" DbType="string" Header="备注2" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="380" AllowQuery="0" QueryOperation="" />
    </Table>      
    
    <Table Name="V_RECORD_LIST">
      <Field Column="RECORD_ID" DbType="String" Header="记录ID" ControlType="" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_ID" DbType="String" Header="任务ID" ControlType="" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="115" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CODE" DbType="String" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="1" QueryOperation="like" />
      <Field Column="PLAN_TYPE_CODE" DbType="String" Header="计划类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select PLAN_TYPE_CODE as value ,PLAN_TYPE_NAME as name from PLAN_TYPE where PLAN_TYPE_FLAG =1 " Remark="" Order="130" AllowQuery="1" QueryOperation="like" />
      <Field Column="MANAGE_RELATE_CODE" DbType="String" Header="任务单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="135" AllowQuery="1" QueryOperation="like" />
      <Field Column="MANAGE_TYPE_CODE" DbType="String" Header="任务类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select MANAGE_TYPE_CODE as value ,MANAGE_TYPE_NAME as name from MANAGE_TYPE where MANAGE_TYPE_FLAG =1 " Remark="" Order="140" AllowQuery="1" QueryOperation="" />
      <Field Column="STOCK_BARCODE" DbType="String" Header="容器条码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_MODEL" Remark="" Order="155" AllowQuery="1" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="like" />
      <Field Column="RECORD_LIST_QUANTITY" DbType="String" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="173" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="176" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="String" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="like" />
      <Field Column="BOX_BARCODE" DbType="String" Header="所在箱格" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="WDZ_BOX_GRID" Remark="" Order="190" AllowQuery="1" QueryOperation="" />
      <Field Column="START_POSITION" DbType="String" Header="开始位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="200" AllowQuery="1" QueryOperation="" />
      <Field Column="END_POSITION" DbType="String" Header="终止位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="210" AllowQuery="1" QueryOperation="" />
      <Field Column="MANAGE_BEGIN_TIME" DbType="String" Header="开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="like" />
      <Field Column="MANAGE_END_TIME" DbType="String" Header="完成时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="like" />
      <Field Column="RECORD_OPERATOR" DbType="String" Header="操作者" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="240" AllowQuery="1" QueryOperation="like" />
      <Field Column="MANAGE_SOURCE" DbType="String" Header="原系统" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="245" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_GROUP" DbType="String" Header="拣选唯一码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="270" AllowQuery="1" QueryOperation="" />
      <Field Column="PLAN_HEADTEXT" DbType="string" Header="拣选抬头文本" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="280" AllowQuery="1" QueryOperation="like" />
    </Table>

    <Table Name="V_STORAGE_LIST">
      <Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="100" AllowQuery="1" QueryOperation="" />
      <Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="105" AllowQuery="1" QueryOperation="" />
      <Field Column="STOCK_BARCODE" DbType="String" Header="容器条码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="111" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_MODEL" Remark="" Order="112" AllowQuery="1" QueryOperation="" />
      <Field Column="BOX_BARCODE" DbType="String" Header="所在箱格" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="WDZ_BOX_GRID" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="like" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="142" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LOCK_QUANTITY" DbType="Decimal" Header="锁定数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="146" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="148" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="ABC分类" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="like" />
      <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="SN码拣选" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="160" AllowQuery="0" QueryOperation="like" />
      <Field Column="PLAN_TO_USER" DbType="" Header="集货号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="168" AllowQuery="1" QueryOperation="like" />
      <Field Column="PLAN_TO_DEPT" DbType="" Header="使用位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="169" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_PROPERTY2" DbType="" Header="异常库存" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="170" AllowQuery="1" QueryOperation="like" />
      <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="like" />
      <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="AREA_ID" DbType="String" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select AREA_ID as VALUE ,AREA_NAME as NAME from WH_AREA " Remark="" Order="185" AllowQuery="1" QueryOperation="" />
      <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="190" AllowQuery="1" QueryOperation="=" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="220" AllowQuery="1" QueryOperation="like" />
      <Field Column="UPDATE_TIME" DbType="String" Header="最后更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="like" />
      <Field Column="LOCK_PLAN_LIST_ID" DbType="Decimal" Header="锁定计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="232" AllowQuery="0" QueryOperation="" />
      <Field Column="IS_EXCEPTION" DbType="Decimal" Header="整箱异常(限制入库)" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="238" AllowQuery="1" QueryOperation="" />
      <Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
      <Field Column="KITBOX_UP_COMPLETE" DbType="Decimal" Header="齐套箱首次上架完成" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="260" AllowQuery="0" QueryOperation="" />
		<Field Column="GOODS_PROPERTY9" DbType="" Header="盘点单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="300" AllowQuery="1" QueryOperation="like" />
		<Field Column="GOODS_PROPERTY10" DbType="" Header="盘点时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="310" AllowQuery="0" QueryOperation="like" />
		<Field Column="GOODS_PROPERTY11" DbType="" Header="库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="320" AllowQuery="0" QueryOperation="like" />
		<Field Column="GOODS_PROPERTY12" DbType="" Header="实际数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="330" AllowQuery="0" QueryOperation="like" />
	</Table>
  </Form>

  <Form Name="DataGridWindow">
    <Table Name="WH_LANEWAY">
      <Field Column="LANEWAY_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_ID" DbType="Int32" Header="存储仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select warehouse_id as value,warehouse_name as name from wh_warehouse" Remark="" Order="2" AllowQuery="1" QueryOperation="" />
      <Field Column="LANEWAY_TYPE" DbType="String" Header="巷道类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="LANEWAY_TYPE" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
      <Field Column="LANEWAY_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="请输入巷道编码" DataBind="" Remark="" Order="4" AllowQuery="1" QueryOperation="" />
      <Field Column="LANEWAY_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="请输入巷道名称" DataBind="" Remark="" Order="5" AllowQuery="1" QueryOperation="" />
      <Field Column="LANEWAY_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
      <Field Column="DEVICE_FLAG" DbType="Int32" Header="巷道设备激活" ControlType="CheckBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_CODE" DbType="数据类型" Header="设备仓库编码" ControlType="控件类型" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    </Table>
    
    <Table Name="WH_CELL">
      <Field Column="CELL_ID" DbType="Int32" Header="货位编号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_ID" DbType="Int32" Header="仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select WAREHOUSE_ID as value , WAREHOUSE_NAME as name from WH_WAREHOUSE where WAREHOUSE_FLAG='1'" Remark="" Order="2" AllowQuery="1" QueryOperation="" />
      <Field Column="AREA_ID" DbType="Int32" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as VALUE , AREA_NAME as NAME from WH_AREA" Remark="" Order="2" AllowQuery="1" QueryOperation="" />
      <Field Column="LOGIC_ID" DbType="Int32" Header="功能区" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select LOGIC_ID as value , LOGIC_NAME as name from WH_LOGIC" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_TYPE" DbType="String" Header="类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_TYPE" Remark="" Order="6" AllowQuery="1" QueryOperation="" />
      <Field Column="DEVICE_CODE" DbType="String" Header="设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="DEVICE_TYPE" Remark="" Order="7" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_Z" DbType="Int32" Header="排" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="8" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_X" DbType="Int32" Header="列" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="9" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_Y" DbType="Int32" Header="层" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_INOUT" DbType="String" Header="出入库" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_INOUT" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="货位尺寸" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_STATUS" DbType="String" Header="货位状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_STATUS" Remark="" Order="13" AllowQuery="1" QueryOperation="" />
      <Field Column="RUN_STATUS" DbType="String" Header="运行状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="RUN_STATUS" Remark="" Order="14" AllowQuery="1" QueryOperation="" />
      <Field Column="CELL_WIDTH" DbType="String" Header="显示宽度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_HEIGHT" DbType="String" Header="显示高度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="21" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_FLAG" DbType="string" Header="是否启用" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="30" AllowQuery="1" QueryOperation="" />
    </Table>
    
    <Table Name="V_RECORD">
      <Field Column="RECORD_ID" DbType="String" Header="" ControlType="" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_ID" DbType="String" Header="" ControlType="" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="RECORD_MESSAGE" DbType="String" Header="记录消息" ControlType="控件类型" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="like" />
      <Field Column="MES_ORDER_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="1" QueryOperation="" />
      <Field Column="TECHNICS_DESCRIPTION" DbType="String" Header="流程名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_TYPE_CODE" DbType="String" Header="" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select MANAGE_TYPE_CODE as value,MANAGE_TYPE_NAME as name FROM MANAGE_TYPE" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
      <Field Column="STOCK_BARCODE" DbType="String" Header="容器条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="1" QueryOperation="" />
      <Field Column="START_CELL_ID" DbType="string" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select cast(CELL_ID as varchar2(50)) AS value , CELL_NAME AS name FROM WH_CELL " Remark="" Order="6" AllowQuery="0" QueryOperation="" />
      <Field Column="END_CELL_ID" DbType="string" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select cast(CELL_ID as varchar2(50)) AS value , CELL_NAME AS name FROM WH_CELL " Remark="" Order="7" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_BEGIN_TIME" DbType="string" Header="开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="260" AllowQuery="1" QueryOperation="like" />
      <Field Column="MANAGE_END_TIME" DbType="string" Header="完成时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="270" AllowQuery="1" QueryOperation="like" />
      <Field Column="MANAGE_CONFIRM_TIME" DbType="String" Header="确认时间" ControlType="" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_OPERATOR" DbType="String" Header="操作人员" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="46" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_STATUS" DbType="String" Header="状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="MANAGE_STATUS" Remark="" Order="45" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CODE" DbType="String" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="50" AllowQuery="1" QueryOperation="like" />
      <Field Column="START_CELL_NAME" DbType="String" Header="开始位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="1" QueryOperation="" />
      <Field Column="END_CELL_NAME" DbType="String" Header="终止位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="1" QueryOperation="" />
      <Field Column="STACK_NO" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="1" QueryOperation="" />
    </Table>
    
    <Table Name="SYS_TABLE_CONVERTER">
      <Field Column="TABLE_CONVERTER_ID" DbType="Decimal" Header="" ControlType="text" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="TABLE_CONVERTER_CODE" DbType="String" Header="转换编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="1" QueryOperation="" />
      <Field Column="TABLE_CONVERTER_NAME" DbType="String" Header="转换名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="1" QueryOperation="" />
      <Field Column="PARENT_TABLE" DbType="String" Header="父级表名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="1" QueryOperation="" />
      <Field Column="PARENT_KEY" DbType="String" Header="父级表主键名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
      <Field Column="CHILD_TABLE" DbType="String" Header="子级表名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="1" QueryOperation="" />
      <Field Column="CHILD_FOREIGN_KEY" DbType="String" Header="子级表外键名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
      <Field Column="REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="SPLIT_PROPERTY_TYPE" DbType="String" Header="拆分类型" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
      <Field Column="SPLIT_PROPERTY_KEY" DbType="String" Header="拆分类型值" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
      <Field Column="SPLIT_PROPERTY_COLUMN" DbType="String" Header="拆分列名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
    </Table>
  </Form>
</Styles>